import { TripDto, TripOrderDto } from "~/types/api/businessConsoleService/MyTrips";
import { Dialog, DialogOverlay, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "../ui/dialog";
import { useEffect, useState } from "react";
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { Separator } from "../ui/separator"
import { Loader2, User, Truck, Package, DollarSign, MapPin, Clock, Phone } from "lucide-react"
import { TripOrderDetailsModal } from "./TripOrderDetailsModal";

/**
 * Trip Details Modal
 * @param tripId - Trip Id
 * @param tripDetails - Trip Details
 * @param onClose - Close Modal Callback
 * @returns
 */
export function TripDetailsModal({
  tripId,
  tripDetails,
  onClose,
}: {
  tripId: number | null
  tripDetails: TripDto | null
  onClose: () => void
}) {
  const [trip, setTrip] = useState<TripDto | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTrip = async (tripId: number) => {
    setLoading(true)
    try {
      const tripResponse = await fetch(`/api/get-trip?tripId=${tripId}&role=seller`)
      if (!tripResponse.ok) throw new Error("Failed to fetch trip details")
      const data = await tripResponse.json()
      setTrip(data)
      setError(null)
    } catch (err) {
      setTrip(null)
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (tripDetails && tripDetails.tripId) {
      setTrip(tripDetails)
    } else if (tripId) {
      fetchTrip(tripId)
    }
  }, [tripId, tripDetails])

  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null)
  const [selectedOrderDetails, setSelectedOrderDetails] = useState<TripOrderDto | null>(null)

  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`
  const formatWeight = (weight: number) => `${weight} kg`

  return (
    <Dialog
      open={!!tripId}
      onOpenChange={(open) => {
        if (!open) {
          onClose()
        }
      }}
    >
      <DialogOverlay className="bg-black/10" />
      <DialogContent className="max-w-4xl rounded-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Trip Details {trip?.tripId && `#${trip.tripId}`}
          </DialogTitle>
        </DialogHeader>

        <div className="max-h-[75vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading trip details...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Error loading trip details: {error}</p>
            </div>
          ) : null}

          {trip && !loading && (
            <div className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Seller Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Name:</span> {trip.sellerName}
                    </p>
                    <p>
                      <span className="font-medium">Mobile:</span> {trip.sellerMobileNumber}
                    </p>
                    <p>
                      <span className="font-medium">ID:</span> {trip.sellerId}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Truck className="h-4 w-4" />
                    Driver Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Name:</span> {trip.driverName}
                    </p>
                    <p>
                      <span className="font-medium">Mobile:</span> {trip.driverMobileNumber}
                    </p>
                    <p>
                      <span className="font-medium">Truck:</span> {trip.truckNumber}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Trip Status & Dates */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold">Status</h3>
                  <Badge variant="outline">{trip.tripStatus}</Badge>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Delivery Date
                  </h3>
                  <p className="text-sm">{trip.deliveryDate}</p>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Service Areas
                  </h3>
                  <p className="text-sm">{trip.serviceAreaList}</p>
                </div>
              </div>

              <Separator />

              {/* Order Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">{trip.completedOrderCount}</div>
                  <div className="text-xs text-muted-foreground">Completed</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{trip.totalOrderCount}</div>
                  <div className="text-xs text-muted-foreground">Total Orders</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{trip.ordersToPick}</div>
                  <div className="text-xs text-muted-foreground">To Pick</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{trip.ordersToDeliver}</div>
                  <div className="text-xs text-muted-foreground">To Deliver</div>
                </div>
              </div>

              <Separator />

              {/* Weight & Amount Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Weight Summary
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Booked:</span>
                      <span className="font-medium">{formatWeight(trip.totalBookedWeight)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Dispatched:</span>
                      <span className="font-medium">{formatWeight(trip.totalDispatchedWeight)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivered:</span>
                      <span className="font-medium">{formatWeight(trip.totalDeliveredWeight)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Returned:</span>
                      <span className="font-medium">{formatWeight(trip.totalReturnedWeight)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cancelled:</span>
                      <span className="font-medium">{formatWeight(trip.totalCancelledWeight)}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Amount Summary
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Booked:</span>
                      <span className="font-medium">{formatCurrency(trip.totalBookedAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivered:</span>
                      <span className="font-medium">{formatCurrency(trip.totalDeliveredAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>COD Collected:</span>
                      <span className="font-medium">{formatCurrency(trip.deliveredCodAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credit Given:</span>
                      <span className="font-medium">{formatCurrency(trip.deliveredCreditGiven)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cash Collected:</span>
                      <span className="font-medium">{formatCurrency(trip.cashCollected)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trip Logistics */}
              {trip.dispatchedTime && (
                <>
                  <Separator />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="font-semibold">Dispatch Details</h3>
                      <div className="text-sm space-y-1">
                        <p>
                          <span className="font-medium">Dispatched By:</span> {trip.dispatchedBy}
                        </p>
                        <p>
                          <span className="font-medium">Time:</span> {trip.dispatchedTime}
                        </p>
                        <p>
                          <span className="font-medium">KM Reading:</span> {trip.dispatchedKmReading}
                        </p>
                        <p>
                          <span className="font-medium">Box Count:</span> {trip.dispatchedBoxCount}
                        </p>
                      </div>
                    </div>

                    {trip.returnedTime && (
                      <div className="space-y-2">
                        <h3 className="font-semibold">Return Details</h3>
                        <div className="text-sm space-y-1">
                          <p>
                            <span className="font-medium">Returned By:</span> {trip.returnedBy}
                          </p>
                          <p>
                            <span className="font-medium">Time:</span> {trip.returnedTime}
                          </p>
                          <p>
                            <span className="font-medium">KM Reading:</span> {trip.returnedKmReading}
                          </p>
                          <p>
                            <span className="font-medium">Trip KMs:</span> {trip.tripKms}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Sales Executive */}
              {trip.salesExecutiveName && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Sales Executive
                    </h3>
                    <div className="text-sm">
                      <p>
                        <span className="font-medium">Name:</span> {trip.salesExecutiveName}
                      </p>
                      <p>
                        <span className="font-medium">Mobile:</span> {trip.salesExecutiveMobile}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        <TripOrderDetailsModal
          orderId={selectedOrderId}
          orderDetails={selectedOrderDetails}
          onClose={() => {
            setSelectedOrderId(null)
            setSelectedOrderDetails(null)
          }}
        />

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
