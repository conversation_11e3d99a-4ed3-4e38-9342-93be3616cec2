import { TripOrderDto } from "~/types/api/businessConsoleService/MyTrips";
import { useEffect, useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogOverlay } from "../ui/dialog"
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { Separator } from "../ui/separator"
import { Loader2, User, Package, DollarSign, MapPin, Clock, Phone, CreditCard } from "lucide-react"

/**
 * Trip Order Details Modal
 * @param orderId - Trip Order Id
 * @param orderDetails - Trip Order Details
 * @param onClose - Close Modal Callback
 * @returns
 */
export function TripOrderDetailsModal({
  orderId,
  orderDetails,
  onClose,
}: {
  orderId: number | null
  orderDetails: TripOrderDto | null
  onClose: () => void
}) {
  const [order, setOrder] = useState<TripOrderDto | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTripOrder = async (orderId: number) => {
    setLoading(true)
    try {
      const tripOrderResponse = await fetch(`/api/get-trip-order?orderId=${orderId}`)
      if (!tripOrderResponse.ok) throw new Error("Failed to fetch order details")
      const data = await tripOrderResponse.json()
      setOrder(data)
      setError(null)
    } catch (err) {
      setOrder(null)
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (orderDetails && orderDetails.orderGroupId) {
      setOrder(orderDetails)
    } else if (orderId) {
      fetchTripOrder(orderId)
    }
  }, [orderId, orderDetails])

  const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`
  const formatWeight = (weight: number) => `${weight} kg`

  return (
    <Dialog
      open={!!orderId}
      onOpenChange={(open) => {
        if (!open) {
          onClose()
        }
      }}
    >
      <DialogOverlay className="bg-black/10" />
      <DialogContent className="max-w-4xl rounded-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Order Details {order?.orderGroupId && `#${order.orderGroupId}`}
          </DialogTitle>
        </DialogHeader>

        <div className="max-h-[75vh] overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading order details...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Error loading order details: {error}</p>
            </div>
          ) : null}

          {order && !loading && (
            <div className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Buyer Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Name:</span> {order.buyerName}
                    </p>
                    <p>
                      <span className="font-medium">ID:</span> {order.buyerId}
                    </p>
                    <p>
                      <span className="font-medium">Primary Contact:</span> {order.primaryContactNumber}
                    </p>
                    {order.secondContactNumber && (
                      <p>
                        <span className="font-medium">Secondary Contact:</span> {order.secondContactNumber}
                      </p>
                    )}
                    <p>
                      <span className="font-medium">Locality:</span> {order.buyerLocality}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Delivery Information
                  </h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Address:</span> {order.address}
                    </p>
                    <p>
                      <span className="font-medium">Service Area:</span> {order.serviceAreaName}
                    </p>
                    <p>
                      <span className="font-medium">Delivery Date:</span> {order.deliveryDate}
                    </p>
                    {order.deliveredTime && (
                      <p>
                        <span className="font-medium">Delivered At:</span> {order.deliveredTime}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Order Status & Payment */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold">Order Status</h3>
                  <Badge variant="outline">{order.orderStatus}</Badge>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">Payment Methods</h3>
                  <div className="flex gap-2">
                    {order.cashAllowed && <Badge variant="secondary">Cash</Badge>}
                    {order.creditAllowed && <Badge variant="secondary">Credit</Badge>}
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">Trip Sequence</h3>
                  <p className="text-sm">#{order.tripSeqNumber}</p>
                </div>
              </div>

              <Separator />

              {/* Order Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">{order.itemCount}</div>
                  <div className="text-xs text-muted-foreground">Items</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{formatWeight(order.weight)}</div>
                  <div className="text-xs text-muted-foreground">Weight</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{order.totalBoxes}</div>
                  <div className="text-xs text-muted-foreground">Boxes</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">{order.pickedItemCount}</div>
                  <div className="text-xs text-muted-foreground">Picked</div>
                </div>
              </div>

              <Separator />

              {/* Amount Breakdown */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Amount Breakdown
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Order Amount:</span>
                      <span className="font-medium">{formatCurrency(order.orderAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivery Charge:</span>
                      <span className="font-medium">{formatCurrency(order.deliveryCharge)}</span>
                    </div>
                    {order.discountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount:</span>
                        <span className="font-medium">-{formatCurrency(order.discountAmount)}</span>
                      </div>
                    )}
                    {order.totalTaxAmount && (
                      <div className="flex justify-between">
                        <span>Tax Amount:</span>
                        <span className="font-medium">{formatCurrency(order.totalTaxAmount)}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-semibold">
                      <span>Total Amount:</span>
                      <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Payment Details
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Cash Collection:</span>
                      <span className="font-medium">{formatCurrency(order.cashCollectionAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credit Amount:</span>
                      <span className="font-medium">{formatCurrency(order.creditAmount)}</span>
                    </div>
                    {order.creditPendingAmount > 0 && (
                      <div className="flex justify-between text-orange-600">
                        <span>Credit Pending:</span>
                        <span className="font-medium">{formatCurrency(order.creditPendingAmount)}</span>
                      </div>
                    )}
                    {order.delayPayment && (
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          Delay Payment
                        </Badge>
                        {order.delayPaymentPaid && (
                          <Badge variant="secondary" className="text-xs">
                            Paid
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Order Items */}
              {order.orderDetail && order.orderDetail.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h3 className="font-semibold">Order Items</h3>
                    <div className="space-y-2">
                      {order.orderDetail.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{item.itemName}</p>
                            <p className="text-xs text-muted-foreground">
                              {item.qty} {item.unit} × {formatCurrency(item.price)}
                            </p>
                            {item.status && (
                              <Badge variant="outline" className="text-xs mt-1">
                                {item.status}
                              </Badge>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-sm">{formatCurrency(item.amount)}</p>
                            {item.cancelledQty > 0 && (
                              <p className="text-xs text-destructive">Cancelled: {item.cancelledQty}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Seller & Sales Executive */}
              <Separator />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h3 className="font-semibold">Seller Information</h3>
                  <div className="text-sm space-y-1">
                    <p>
                      <span className="font-medium">Name:</span> {order.sellerName}
                    </p>
                    {order.sellerContactNumber && (
                      <p>
                        <span className="font-medium">Contact:</span> {order.sellerContactNumber}
                      </p>
                    )}
                  </div>
                </div>

                {order.salesExecutiveName && (
                  <div className="space-y-2">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Sales Executive
                    </h3>
                    <div className="text-sm space-y-1">
                      <p>
                        <span className="font-medium">Name:</span> {order.salesExecutiveName}
                      </p>
                      <p>
                        <span className="font-medium">Mobile:</span> {order.salesExecutiveMobile}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Delivery Details */}
              {order.deliveredBy && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Delivery Details
                    </h3>
                    <div className="text-sm space-y-1">
                      <p>
                        <span className="font-medium">Delivered By:</span> {order.deliveredBy}
                      </p>
                      <p>
                        <span className="font-medium">Contact:</span> {order.deliveredByContactNumber}
                      </p>
                      <p>
                        <span className="font-medium">Time:</span> {order.deliveredTime}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
