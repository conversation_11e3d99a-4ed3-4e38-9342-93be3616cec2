import { ActionFunction, LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate, useLocation, useFetcher } from "@remix-run/react";

import { Card, CardContent } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import { ArrowLeft, User } from "lucide-react";
import { BuyerCreditSummary, getBuyerCreditSummary, DriverCreditSummary, recieveSellerCashFromDriver } from "~/services/myDrivers";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { useEffect, useState } from "react";
import { Checkbox } from "~/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogOverlay, DialogTitle } from "~/components/ui/dialog";
import { useToast } from "~/components/ui/ToastProvider";
import ShowDecimalAsSubscript from "~/components/common/ShowDecimalAsSubscript";
import { format, toZonedTime } from "date-fns-tz";

interface LoaderData {
  buyerCreditSummary: BuyerCreditSummary[];
  driverUserId: number;
  error?: string;
}

type ActionIntent = "Recieve Cash";

interface ActionData {
  intent: ActionIntent;
  success: boolean;
  error?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request, params }) => {
  const driverUserId = params?.id;

  try {
    const response = await getBuyerCreditSummary(Number(driverUserId), request);
    return withResponse({
      buyerCreditSummary: response.data,
      driverUserId: Number(driverUserId)
    }, response.headers);
  } catch (error) {
    return withResponse({
      buyerCreditSummary: [],
      driverUserId: Number(driverUserId),
      error: "Failed to load buyer credit summary"
    }, new Headers());
  }
});

export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "Recieve Cash") {
    try {
      const { ctSelected, driverUserId } = JSON.parse(formData.get("data") as string);
      await recieveSellerCashFromDriver(ctSelected, driverUserId, request);
      return { success: true };
    } catch (error) {
      return { success: false, error: "Failed to recieve cash" };
    }
  }

  return { success: false, error: "Invalid intent" };
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatTime = (dateString: string) => {
  const istTime = toZonedTime(dateString, "Asia/Kolkata");
  return format(istTime, "dd/MM hh:mm a");
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export default function MyDriver() {
  const { buyerCreditSummary, driverUserId, error } = useLoaderData<LoaderData>();
  const navigate = useNavigate();
  const location = useLocation();

  // action
  const fetcher = useFetcher<ActionData>();
  const isSubmitting = fetcher.state === "submitting" || fetcher.state === "loading";
  const { showToast } = useToast();

  // Get driver details from location state (passed from the drivers list)
  const driverDetails = location.state?.driverDetails as DriverCreditSummary | undefined;

  const [ctSelected, setCtSelected] = useState<Set<number>>(new Set());
  const [openCTModalFrom, setOpenCTModalFrom] = useState(false);

  const handleCheckBoxChange = (checked: boolean | string, cashTrackerId: number | undefined) => {
    if (!cashTrackerId) return;
    if (checked) {
      setCtSelected((prev) => {
        const newSet = new Set(prev);
        newSet.add(cashTrackerId);
        return newSet;
      });
    } else {
      setCtSelected((prev) => {
        const newSet = new Set(prev);
        newSet.delete(cashTrackerId);
        return newSet;
      })
    }
  }

  function getBuyerRecievedSum(
    nBuyerId: number,
    selectedIds: Set<number>
  ): number {
    const buyer = buyerCreditSummary.find(b => b.nBuyerId === nBuyerId);
    if (!buyer || !buyer.ogCashTracker) return 0;

    return buyer.ogCashTracker.reduce((sum, tracker) => {
      if (tracker.cashTrackerId && selectedIds.has(tracker.cashTrackerId)) {
        const cod = tracker.codCollected ?? 0;
        const credit = tracker.creditPendingCollected ?? 0;
        return sum + cod + credit;
      }
      return sum;
    }, 0);
  }

  function getTotalRecievedSum(selectedIds: Set<number>): number {
    return buyerCreditSummary.reduce((sum, buyer) => {
      return sum + getBuyerRecievedSum(buyer.nBuyerId, selectedIds);
    }, 0);
  }

  function getTotalCashCollectedSum(): number {
    return buyerCreditSummary.reduce((sum, buyer) => {
      return sum + (buyer.totalCollection ?? 0);
    }, 0);
  }

  function getTotalCreditBalanceSum(): number {
    return buyerCreditSummary.reduce((sum, buyer) => {
      return sum + (buyer.totalCreditPending ?? 0);
    }, 0);
  }

  const handleSelectAllCt = () => {
    const allCashTrackerIds = buyerCreditSummary
      .flatMap(buyer => buyer.ogCashTracker?.map(ct => ct.cashTrackerId) ?? [])
      .filter((id): id is number => id !== undefined);

    setCtSelected(new Set(allCashTrackerIds));
  };

  const handleClearAllCt = () => {
    setCtSelected(new Set());
  }

  const handleSubmitCT = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("intent", "Recieve Cash");
    formData.append("data", JSON.stringify({ ctSelected: Array.from(ctSelected), driverUserId }));
    fetcher.submit(formData, { method: "post" });
    setOpenCTModalFrom(false);
  }

  useEffect(() => {
    if (fetcher.data?.success) {
      showToast("Cash collected successfully", "success");
      setCtSelected(new Set());
    } else if (fetcher.data?.error) {
      showToast("Failed to collect cash", "error");
    }
  }, [fetcher.data]);

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-7xl">
      {isSubmitting && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 pointer-events-none z-50 p-4"
        >
          <div className="p-6 text-center">
            <div className="w-20 h-20 border-4 border-transparent border-t-blue-400 rounded-full animate-spin flex items-center justify-center">
              <div className="w-16 h-16 border-4 border-transparent border-t-red-400 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* Page Header */}
        <div>
          <Button variant="secondary" size="sm" onClick={() => navigate(-1)} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Drivers
          </Button>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-lg sm:text-2xl font-bold text-gray-900 break-words">
                {driverDetails?.driverName}
              </h1>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center sm:justify-end">
              <p className="text-base sm:text-lg font-medium text-muted-foreground">
                Cash Received / Reconciled:
              </p>
              <div className="text-lg sm:text-xl font-semibold">
                <p>
                  ₹{" "}
                  {getTotalRecievedSum(ctSelected)
                    ? <ShowDecimalAsSubscript value={formatCurrency(getTotalRecievedSum(ctSelected))} decFontSize="13px" />
                    : "0.00"}
                </p>
              </div>
              <Button
                className="w-full sm:w-auto px-6 sm:px-8 sm:mr-4"
                disabled={ctSelected.size === 0}
                onClick={() => setOpenCTModalFrom(true)}
              >
                Collect
              </Button>
            </div>
          </div>
        </div>

        {error ? (<div className="p-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        </div>) : null}

        <Card>
          <CardContent className="p-0">
            {buyerCreditSummary && buyerCreditSummary.length > 0 ? (
              <div>
                <Table>
                  <TableHeader>
                    <TableRow className="bg-white hover:bg-gray-50">
                      <TableHead></TableHead>
                      <TableHead className="text-xs whitespace-nowrap">Order ID</TableHead>
                      <TableHead className="text-xs whitespace-nowrap">Delivery Date</TableHead>
                      <TableHead className="text-xs whitespace-nowrap">Collection Time</TableHead>
                      <TableHead className="text-xs whitespace-nowrap text-right">
                        <p>Cash Collected (₹)</p>
                        {getTotalCashCollectedSum() ? (
                          <p className="text-base text-green-600 font-semibold">( T: <ShowDecimalAsSubscript value={formatCurrency(getTotalCashCollectedSum())} decFontSize="11px" /> )</p>
                        ) : null}
                      </TableHead>
                      <TableHead className="text-xs whitespace-nowrap text-right bg-yellow-50 hover:bg-yellow-50">
                        <div className="flex flex-col justify-end">
                          <div>Cash Recieved (₹)</div>
                          <div className="py-1 text-sm whitespace-nowrap text-yellow-400"><span onClick={handleSelectAllCt} className="cursor-pointer hover:underline">Select All</span>&nbsp;&nbsp;<span onClick={handleClearAllCt} className="cursor-pointer hover:underline">Clear</span></div>
                        </div>
                      </TableHead>
                      <TableHead className="text-xs whitespace-nowrap text-right">
                        <p>Credit Balance (₹)</p>
                        {getTotalCreditBalanceSum() ? (
                          <p className="text-base text-orange-600 font-semibold">( T: <ShowDecimalAsSubscript value={formatCurrency(getTotalCreditBalanceSum())} decFontSize="11px" /> )</p>
                        ) : null}
                      </TableHead>
                    </TableRow>
                  </TableHeader>

                  <TableBody>
                    {buyerCreditSummary.map((buyer) => {
                      return (
                        <>
                          <TableRow key={'buyer-' + buyer.nBuyerId} className="bg-gray-100 hover:bg-gray-100">
                            <TableCell className="p-3">
                              <p className="font-medium text-gray-900">{buyer.buyerName}</p>
                              {buyer.locality && <p className="text-sm text-gray-500">{buyer.locality}</p>}
                            </TableCell>
                            <TableCell colSpan={3} className="p-3"></TableCell>
                            <TableCell className="text-sm text-right p-3">
                              <p className="font-medium text-green-600">
                                {buyer.totalCollection ? <ShowDecimalAsSubscript value={formatCurrency(buyer.totalCollection)} decFontSize="10px" /> : "-"}
                              </p>
                            </TableCell>
                            <TableCell className="p-3 text-right bg-yellow-50 hover:bg-yellow-50">
                              {getBuyerRecievedSum(buyer.nBuyerId, ctSelected) ? <ShowDecimalAsSubscript value={formatCurrency(getBuyerRecievedSum(buyer.nBuyerId, ctSelected))} decFontSize="10px" /> : ""}
                            </TableCell>
                            <TableCell className="text-sm text-right p-3">
                              <p className="font-medium text-orange-600">
                                {buyer.totalCreditPending ? <ShowDecimalAsSubscript value={formatCurrency(buyer.totalCreditPending)} decFontSize="10px" /> : "-"}
                              </p>
                            </TableCell>
                          </TableRow>

                          {buyer?.ogCashTracker?.map((ct) => (
                            <TableRow key={ct.orderGroupId} className="bg-white hover:bg-white">
                              <TableCell className="p-3"></TableCell>
                              <TableCell className="text-xs p-3">{ct.orderGroupId}</TableCell>
                              <TableCell className="text-xs p-3 whitespace-nowrap">{formatDate(ct.deliveryDate)}</TableCell>
                              <TableCell className="text-xs p-3">{ct.collectionTime ? formatTime(ct.collectionTime) : ""}</TableCell>
                              <TableCell className="text-xs p-3 text-right">
                                {((ct.creditPendingCollected || 0) + (ct.codCollected || 0)) > 0 ? <ShowDecimalAsSubscript value={formatCurrency((ct.creditPendingCollected || 0) + (ct.codCollected || 0))} decFontSize="10px" /> : "-"}
                              </TableCell>
                              <TableCell className="p-3 bg-yellow-50 hover:bg-yellow-50" align="right">
                                {ct.cashTrackerId ? (
                                  <Checkbox checked={ctSelected.has(ct.cashTrackerId)} onCheckedChange={(checked) => handleCheckBoxChange(checked, ct.cashTrackerId)} className="h-4 w-4 mr-8" />
                                ) : ""}
                              </TableCell>
                              <TableCell className="text-xs p-3 text-right">
                                {ct.creditPendingAmount ? <ShowDecimalAsSubscript value={formatCurrency(ct.creditPendingAmount)} decFontSize="10px" /> : "-"}
                              </TableCell>
                            </TableRow>
                          ))}
                        </>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-600">No credits found</p>
                    <p className="text-sm text-gray-500">No credit information available for this driver</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Dialog open={openCTModalFrom} onOpenChange={setOpenCTModalFrom}>
        <DialogOverlay className="bg-black/10" />
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Collect Cash</DialogTitle>
          </DialogHeader>
          <div className="sm:max-w-screen-sm max-h-96 overflow-auto rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-left">Buyer</TableHead>
                  <TableHead className="text-right">Selected Amount (₹)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {buyerCreditSummary.map((buyer) => {
                  const buyerSum = getBuyerRecievedSum(buyer.nBuyerId, ctSelected);
                  if (buyerSum === 0) return null;

                  return (
                    <TableRow key={buyer.nBuyerId}>
                      <TableCell className="font-medium">{buyer.buyerName}</TableCell>
                      <TableCell className="text-right">
                        <ShowDecimalAsSubscript value={formatCurrency(buyerSum)} decFontSize="11px" />
                      </TableCell>
                    </TableRow>
                  );
                })}
                <TableRow className="bg-gray-100 hover:bg-gray-100 font-semibold">
                  <TableCell>Total</TableCell>
                  <TableCell className="text-right">
                    <ShowDecimalAsSubscript value={formatCurrency(getTotalRecievedSum(ctSelected))} decFontSize="11px" />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => setOpenCTModalFrom(false)}>
              Cancel
            </Button>
            <Button type="submit" onClick={handleSubmitCT}>Collect</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
