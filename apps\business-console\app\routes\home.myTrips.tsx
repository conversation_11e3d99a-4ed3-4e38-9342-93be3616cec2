import { <PERSON>, json, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, useNavigate } from "@remix-run/react";
import { format } from "date-fns";
import { CalendarIcon, ChevronsLeft, ChevronsRight } from "lucide-react";
import { useEffect, useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Calendar } from "~/components/ui/calendar";
import { Input } from "~/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { withAuth, withResponse } from "@utils/auth-utils";
import { getSellerTrips } from "~/services/myTripsService";
import { TripSummaryDto } from "~/types/api/businessConsoleService/MyTrips";
import { TripDetailsModal } from "~/components/mytrip/TripDetailsModal";

interface LoaderData {
      data: TripSummaryDto[];
      date: string;
      error?: string;
}

export const loader = withAuth(async ({ request }) => {
      try {
            const url = new URL(request.url);
            const date = url.searchParams.get("date") || new Date().toISOString().split("T")[0];

            if (!date) {
                  throw json(
                        { error: "Date is required" },
                        { status: 400 }
                  );
            }
            const response = await getSellerTrips(date, request);
            return withResponse({ data: response.data, date }, response.headers);
      } catch (error) {
            return withResponse({ data: [], error: "Failed to fetch trips" }, new Headers());
      }
});

export default function MyTrips() {
      const { data, date, error } = useLoaderData<LoaderData>();
      const navigate = useNavigate();

      const [searchTerm, setSearchTerm] = useState("");

      const [selectedTripId, setSelectedTripId] = useState<number | null>(null);

      const [currentPage, setCurrentPage] = useState(1)
      const itemsPerPage = 100
      const totalPages = Math.ceil(data.length / itemsPerPage);
      const startIndex = (currentPage - 1) * itemsPerPage;
      const currentTrips = data.slice(startIndex, startIndex + itemsPerPage);

      const handleSubmit = (date: Date | undefined) => {
            if (!date) return;
            const deliveryDate = format(date, "yyyy-MM-dd")
            navigate(`/home/<USER>
      }

      const isDisabledDay = (day: Date) => {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            return day > tomorrow;
      }

      const filterTrips = (trip: TripSummaryDto) => {
            return (
                  (trip.driverName?.toLowerCase() || "").includes(searchTerm.toLowerCase())
            );
      }

      const handleSetPage = (page: number) => {
            if (page >= 1 && page <= totalPages) {
                  setCurrentPage(page)
            }
      }

      if (error) {
            return <div>Error: {error}</div>;
      }

      return (
            <div className="container mx-auto w-full py-4 sm:py-6" >

                  {/* Heading */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div>
                              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Trips</h1>
                              <p className="text-sm sm:text-base text-gray-600 mt-1">View your trips and trip history</p>
                        </div>
                  </div>

                  {/* Filters */}
                  <div className="flex flex-col md:flex-row my-6 gap-6 md:gap-10">
                        <div className="w-fit">
                              <Popover>
                                    <div className="flex flex-row gap-1 items-center justify-between">
                                          <Button variant="ghost" size="icon"
                                                onClick={() => {
                                                      const newDate = new Date(date);
                                                      newDate.setDate(newDate.getDate() - 1)
                                                      handleSubmit(newDate)
                                                }}
                                          >
                                                <ChevronsLeft className="w-5 h-5" />
                                          </Button>
                                          <PopoverTrigger asChild>
                                                <Button variant="outline" className="w-[220px]">
                                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                                      {date ? format(date, "PPP") : "Pick a date"}
                                                </Button>
                                          </PopoverTrigger>
                                          <Button variant="ghost" size="icon"
                                                onClick={() => {
                                                      const newDate = new Date(date);
                                                      newDate.setDate(newDate.getDate() + 1)
                                                      handleSubmit(newDate)
                                                }}
                                                disabled={isDisabledDay(new Date(new Date(date).setDate(new Date(date).getDate() + 1)))}
                                          >
                                                <ChevronsRight className="w-5 h-5" />
                                          </Button>
                                    </div>
                                    <PopoverContent className="w-auto p-0" >
                                          <Calendar
                                                mode="single"
                                                selected={date ? new Date(date) : undefined}
                                                onSelect={(newDate) => {
                                                      if (newDate) {
                                                            handleSubmit(newDate)
                                                      }
                                                }}
                                                initialFocus
                                                disabled={(day) => isDisabledDay(day)}
                                          />
                                    </PopoverContent>
                              </Popover>
                        </div>
                        <Input placeholder="Search By Driver Name"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>

                  {/* Trip List */}
                  <Table>
                        <TableHeader className="bg-gray-100">
                              <TableHead>Driver</TableHead>
                              {/* <TableHead>Areas</TableHead> */}
                              <TableHead>Status</TableHead>
                              <TableHead className="whitespace-nowrap text-center">Delivered / Booked</TableHead>
                              {/* <TableHead className="text-center">Cancelled Orders</TableHead>
                              <TableHead className="text-center">Cancelled Qty (kg)</TableHead>
                              <TableHead className="text-center">Qty (kg)</TableHead> */}
                        </TableHeader>
                        <TableBody>
                              {currentTrips.length > 0 ? currentTrips.sort((a, b) => {
                                    const Priority: { [key: string]: number } = { Dispatched: 1, Open: 2 };
                                    return (Priority[a.tripStatus] || 3) - (Priority[b.tripStatus] || 3);
                              }).filter((trip) => filterTrips(trip)).map((x) => (
                                    <TableRow key={x.tripId}>
                                          <TableCell>{x.driverName}</TableCell>
                                          {/* <TableCell>areas</TableCell> */}
                                          <TableCell className={x.tripStatus === "Dispatched" ? "text-red-500" : x.tripStatus === "Open" ? "text-orange-500" : "text-green-600"}>{x.tripStatus}</TableCell>
                                          <TableCell className="whitespace-nowrap text-center">{x.totalDeliveredOrders} / {x.totalOrders}</TableCell>
                                          {/* <TableCell className="text-center">{x.cancelledOrders}</TableCell>
                                          <TableCell className="text-center">{x.totalCancelledWeight}</TableCell>
                                          <TableCell className="text-center">{x.totalWeight}</TableCell> */}
                                    </TableRow>
                              )) : (
                                    <TableRow>
                                          <TableCell
                                                colSpan={9}
                                                className="h-24 text-center"
                                          >
                                                No results.
                                          </TableCell>
                                    </TableRow>
                              )
                              }
                        </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center space-x-2 my-2">
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSetPage(currentPage - 1)}
                              disabled={currentPage === 1}
                        >
                              Previous
                        </Button>
                        <span className="text-gray-700">
                              Page {currentPage} of {totalPages}
                        </span>
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                    handleSetPage(currentPage + 1)
                              }
                              disabled={currentPage === totalPages}
                        >
                              Next
                        </Button>
                  </div>

                  {/* Trip Details Modal */}
                  <TripDetailsModal tripId={selectedTripId} onClose={() => setSelectedTripId(null)} tripDetails={null} />
            </div>
      )
}
